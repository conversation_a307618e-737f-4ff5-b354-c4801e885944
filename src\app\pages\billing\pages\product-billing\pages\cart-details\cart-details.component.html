<div class="o-sidebar-wrapper">
    <div class="o-sidebar-header">
        <div class="title">Products in Order #{{ selectedCartItems?.cartId }}</div>
        <div class="action-btn-wrapper">
            <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button"
                (click)="closeSideNavFun()">Close</button>
        </div>
    </div>
    <div class="divider"></div>
    <div class="o-sidebar-body">
        <div class="cart-items-container">
            <div class="cart-wrapper">
                <div class="cart-header">Product Details</div>
                @for (item of selectedCartItems?.cartItems; track $index) {
                    <div class="cart-item">
                        <div class="product-wrapper">
                            <div class="index-number" *ngIf="selectedCartItems?.cartItems?.length! > 1">{{ $index + 1 }}.</div>
                            <img [src]="item.productImage" class="product-image" alt="product" />
                            <div>
                                <div class="plan-name">{{ item.productName }}</div>
                                <div class="plan-content">{{ item.productDescription }}</div>
                            </div>
                        </div>
                        <div class="plan-pricing">
                            <div class="fw-bold">${{ item.productPrice | number:'1.2-2' }}</div>
                            <div class="text-gray">{{ item.quantity }} items</div>
                        </div>
                    </div>
                    <div class="dotted-divider" *ngIf="!$last"></div>
                }
            </div>
            <div class="payment-slip">
                <div class="cart-header">Payment Details</div>
                <div class="payment-slip-item">
                    <div class="payment-slip-label">Total Amount</div>
                    <div class="payment-slip-value">${{ selectedCartItems?.totalAmount | number:'1.2-2' }}</div>
                </div>
                <div class="payment-slip-item">
                    <div class="payment-slip-label">Discount Amount</div>
                    <div class="payment-slip-value">-${{ selectedCartItems?.discountAmount | number:'1.2-2' }}</div>
                </div>
                <div class="dotted-divider"></div>
                <div class="payment-slip-item">
                    <div class="payment-slip-label">Paid Amount</div>
                    <div class="payment-slip-value">${{ selectedCartItems?.paidAmount | number:'1.2-2' }}</div>
                </div>
            </div>
        </div>
    </div>
</div>