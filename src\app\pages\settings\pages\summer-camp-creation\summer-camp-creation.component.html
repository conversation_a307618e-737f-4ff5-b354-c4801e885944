<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isAddSummerCampSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-750"
    [disableClose]="true">
    <ng-container [ngTemplateOutlet]="selectedSummerCamp ? updateOrViewSummerCamp : addSummerCamp"></ng-container>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-with-btn">
      <div class="tab-item-content">
        @for (pageTabOption of pageTabOptions | keyvalue: keepOriginalOrder; track $index) {
          <div
            [ngClass]="{ item: true, 'active-item': selectedTabOption === pageTabOption.value }"
            (click)="setActiveTabOption(pageTabOption.value)">
            {{ pageTabOption.value }}
          </div>
        }
      </div>
      <div class="action-btn-wrapper">
        <button
          mat-raised-button
          color="primary"
          class="mat-primary-btn action-btn"
          type="button"
          (click)="toggleAddEditSideNav(true, false, null)">
          Add Summer Camp
        </button>
      </div>
    </div>

    <div class="auth-page-wrapper auth-page-with-header" [ngClass]="{ 'hide-pagination': totalCount < 11 }">
      <div class="filter-and-count-wrapper mb-2">
        <div class="search-and-count-wrapper-auth">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input
                matInput
                placeholder="Search.."
                [(ngModel)]="filters.searchFilter"
                (ngModelChange)="onSearchTermChanged()" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <div class="filter-wrapper">
          <div class="search-bar-wrapper">->
            <app-multi-select
              [filterDetail]="filters.locationId"
              (selectedFilterValues)="getSummerCampDetail((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>

          <mat-form-field class="search-bar-wrapper">
            <mat-select
              [(ngModel)]="filters.ageGroup"
              (selectionChange)="getSummerCampDetail((currentPage = 1), pageSize)">
              <mat-option [value]="all.ALL">All Age Group</mat-option>
              <mat-option *ngFor="let age of constants.ageOptions" [value]="age.value">
                {{ age.label }} Age Group
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : summerCampLists"></ng-container>
    </div>
    @if (totalCount > 10) {
      <pagination-controls
        id="summer-camp"
        [previousLabel]="''"
        [nextLabel]="''"
        (pageChange)="onPageChange($event)"
        [responsive]="true"
        class="pagination-controls"></pagination-controls>
    }
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #addSummerCamp>
  <app-add-summer-camp
    (closeSideNav)="toggleAddEditSideNav(false, false, null)"
    (isSummerCampAdded)="getSummerCampDetail(currentPage, pageSize)"></app-add-summer-camp>
</ng-template>

<ng-template #updateOrViewSummerCamp>
  @if (isViewSummerCampSideNavOpen) {
    <app-view-summer-camp
      (closeViewSideNav)="toggleAddEditSideNav(false, false, null)"
      (summerCampUpdated)="getSummerCampDetail(currentPage, pageSize)"
      (openEditSideNav)="toggleAddEditSideNav(true, false, selectedSummerCamp, true)"
      [selectedTabOption]="selectedTabOption"
      [selectedSummerCamp]="selectedSummerCamp"></app-view-summer-camp>
  } @else {
    <app-update-summer-camp
      (closeSideNav)="toggleAddEditSideNav(isEditFromView, isEditFromView, isEditFromView ? selectedSummerCamp : null)"
      (isSummerCampUpdated)="getSummerCampDetail(currentPage, pageSize)"
      [selectedSummerCamp]="selectedSummerCamp"></app-update-summer-camp>
  }
</ng-template>

<ng-template #summerCampLists>
  <ng-container [ngTemplateOutlet]="summerCampDetails.length ? summerCampList : noDataFound"></ng-container>
</ng-template>

<ng-template #summerCampList>
  <div class="summer-camp-list" [ngClass]="{ 'hide-pagination': totalCount < 11 }">
    @for (
      summerCampDetail of summerCampDetails
        | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "summer-camp" };
      track $index
    ) {
      <div class="o-card mb-2">
        <div class="o-card-body">
          <div class="pointer" (click)="toggleAddEditSideNav(true, true, summerCampDetail)">
            <div class="title">
              {{ summerCampDetail.summerCampScheduleSummary.campName | titlecase }} ({{
                schedulerService.getAgeLabelFromValue(summerCampDetail.summerCampScheduleSummary.ageGroup)
              }})
            </div>
            <div class="summer-camp-content">
              <div>
                {{ summerCampDetail.summerCampScheduleSummary.scheduleStartDate | date: "mediumDate" }}
                - {{ summerCampDetail.summerCampScheduleSummary.scheduleEndDate | date: "mediumDate" }}
              </div>
              <div class="dot"></div>
              <div>
                {{ summerCampDetail.summerCampScheduleSummary.scheduleStartTime | date: "shortTime" }}
                - {{ summerCampDetail.summerCampScheduleSummary.scheduleEndTime | date: "shortTime" }}
              </div>
              <div class="dot"></div>
              <div class="primary-color">
                {{
                  schedulerService.getNumberOfWeeks(
                    summerCampDetail.summerCampScheduleSummary.scheduleStartDate,
                    summerCampDetail.summerCampScheduleSummary.scheduleEndDate
                  )
                }}
                Weeks
              </div>
              <div class="dot"></div>
              <div class="text-gray">
                {{ summerCampDetail.summerCampScheduleSummary.locationName }}
              </div>
            </div>
          </div>
          <div class="summer-camp-pricing">
            <div class="title mb-0 pe-2">${{ summerCampDetail.summerCampScheduleSummary.price }}</div>
            <img
              *ngIf="selectedTabOption === pageTabOptions.UPCOMING"
              class="pointer" alt=""
              [src]="constants.staticImages.icons.editPenGray"
              (click)="toggleAddEditSideNav(true, false, summerCampDetail)" />
            <img
              class="pointer" alt=""
              [src]="constants.staticImages.icons.trash"
              (click)="deleteSummerCampConfirmation(summerCampDetail.summerCampScheduleSummary.id)" />
          </div>
        </div>
      </div>
    }
  </div>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <app-content-loader></app-content-loader>
</ng-template>
