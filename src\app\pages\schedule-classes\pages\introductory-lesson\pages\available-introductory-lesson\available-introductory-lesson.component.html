<div class="available-appointment-list-wrapper">
  <div class="filter-wrapper">
    <div class="date-time-slot-wrapper">
      <div class="date-range">
        <mat-form-field>
          <mat-date-range-input [rangePicker]="picker" [min]="currentDate">
            <input matStartDate placeholder="Start date" [(ngModel)]="startDate" (click)="picker.open()" />
            <input
              matEndDate
              placeholder="End date"
              [(ngModel)]="endDate"
              (ngModelChange)="onDateRangeSelected()"
              (click)="picker.open()" />
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>
      </div>
      <div class="time-range">
        <mat-form-field>
          <input matInput [(ngModel)]="selectedTime" (click)="timePicker.open()" placeholder="Select Time Slot" />
          <mbsc-datepicker
            theme="ios"
            themeVariant="light"
            [ariaHidden]="false"
            [controls]="['time']"
            select="range"
            display="anchored"
            [touchUi]="true"
            (onChange)="setTimeFilter($event)"
            #timePicker></mbsc-datepicker>
        </mat-form-field>
      </div>
    </div>
    <div class="select-instructor-wrapper" *ngIf="!isPassReschedule" (click)="openInstructorSideNav(true)">
      <div>
        <span class="select-instructor-btn">Select Instructor</span>:
        <span class="selected-instructor-count">{{
          filteredInstructorCount && selectedInstructorsIdFromParent.length === filteredInstructorCount
            ? "All (" + selectedInstructorsIdFromParent.length + ")"
            : selectedInstructorsIdFromParent.length + (filteredInstructorCount ? " of " + filteredInstructorCount : "")
        }}</span>
      </div>
      <mat-icon>keyboard_arrow_right</mat-icon>
    </div>
  </div>
  <div class="appointment-list-wrapper">
    <div class="date-header-wrapper">
      @for (item of dateRange; track $index) {
        <div
          class="header-content"
          [ngClass]="{ 'schedule-date-active': item.date === selectedDate }"
          (click)="setScheduleDate(item.date)">
          <div class="date">{{ item?.date | date: "mediumDate" }}</div>
          <div class="day">{{ item?.date | date: constants.dateFormats.day }}</div>
        </div>
      }
    </div>
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : appointmentContent"></ng-container>
  </div>
</div>

<ng-template #appointmentContent>
  <ng-container
    [ngTemplateOutlet]="
      appointments && appointments.length ? appointmentsList : noAppointmentsAvailable
    "></ng-container>
</ng-template>

<ng-template #appointmentsList>
  <div class="time-slot-wrapper">
    @for (item of appointments; track $index) {
      <div class="time-slot-content-wrapper">
        @for (slots of item.introductoryLessonsDetails; track $index) {
          <div
            [ngClass]="{ 'time-slot-content': true, 'time-slot-content-active': slots.id === selectedTimeSlotId }"
            (click)="setSelectedSlotIdAndShowInstructorDetailsFlag(slots, false, $event)">
            <div class="time-slot">{{ slots.startTime | date: "hh:mm a" }} - {{ slots.endTime | date: "hh:mm a" }}</div>
            <div class="staff-wrapper" (click)="setSelectedSlotIdAndShowInstructorDetailsFlag(slots, true, $event)">
              <img [src]="constants.staticImages.images.profileImgPlaceholder" alt="" class="staff-img" />
              <div class="staff-name">{{ slots.instructorName }}</div>
            </div>
          </div>
        }
      </div>
    }
  </div>
</ng-template>

<ng-template #noAppointmentsAvailable>
  <div class="no-appointments-available-wrapper">
    <div class="no-appointments-available-content">
      <div class="description">There is <span class="d-bold">no instructor available</span> at this moment.</div>
    </div>
  </div>
</ng-template>

<ng-template #showLoader>
  <app-content-loader></app-content-loader>
</ng-template>
