<div class="instructor-list-wrapper o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">Instructors</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeInstructorSideNavFn()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="selectInstructorsAndClose()">
        Select
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="instructor-list-content">
    <div class="filter-panel">
      <div class="search-and-count-wrapper">
        <div class="total-instructor-selected">
          Instructor <span class="total-instructor-selected-count">{{ selectedInstructorsId.length }}</span>
        </div>
        <div class="search-bar">
          <mat-form-field class="search-bar-wrapper">
            <input matInput placeholder="Search" [(ngModel)]="searchTerm" (ngModelChange)="onSearchTermChanged()" />
            <mat-icon matTextPrefix>search</mat-icon>
          </mat-form-field>
        </div>
      </div>
      <div class="select-all-checkbox">
        <mat-checkbox [ngModel]="isAllInstructorSelected" (change)="selectUnselectAllInstructors()">
          Select All</mat-checkbox
        >
      </div>
    </div>
    <div class="instructor-list">
      @for (instructor of instructors; track $index) {
        <div class="instructor-info-wrapper">
          <div class="instructor-img">
            <img
              [src]="
                instructor.instructorDetail.profilePhoto
                  ? instructor.instructorDetail.profilePhoto
                  : constants.staticImages.images.profileImgPlaceholder
              "
              alt="" />
          </div>
          <div class="instructor-info">
            <div class="instructor-name-wrapper">
              <div class="name">{{ instructor.instructorDetail.name }}</div>
              <div class="checkbox">
                <mat-checkbox
                  [ngModel]="isInstructorIdPresentInSelectedInstructors(instructor.instructorDetail.id)"
                  (change)="selectInstructor(instructor.instructorDetail.id)"></mat-checkbox>
              </div>
            </div>
            <div class="instructor-description">
              <div
                *ngIf="
                  readMoreTextIndex === $index ||
                  (instructor?.instructorDetail?.bio?.length && instructor.instructorDetail.bio.length < 500)
                ">
                {{ instructor.instructorDetail.bio }}
              </div>
              <div
                *ngIf="
                  readMoreTextIndex !== $index &&
                  instructor?.instructorDetail?.bio?.length &&
                  instructor.instructorDetail.bio.length > 500
                ">
                {{ instructor.instructorDetail.bio | slice: 0 : 500 }}
                <span class="read-more" (click)="readMoreTextIndex = $index">Read More</span>
              </div>
            </div>
          </div>
        </div>
      }
    </div>
  </div>
</div>
