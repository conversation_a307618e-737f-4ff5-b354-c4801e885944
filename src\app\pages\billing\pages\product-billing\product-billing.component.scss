@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.auth-page-wrapper {
  overflow: hidden !important;
  height: calc(100vh - 120px) !important;
  padding: 20px 20px 0px 20px !important;

  .search-and-count-wrapper-auth {
    justify-content: space-between;

    .search-and-count-wrapper {
      @include flex-content-align-center;
    }
  }

  .no-data-found-card {
    height: calc(100vh - 150px) !important;
  }

  ::ng-deep {
    .mdc-text-field {
      background-color: $white-color !important;
    }
    .mat-mdc-form-field {
      width: 100% !important;
    }
  }
}

.user-product-container {
  .user-product-wrapper {
    overflow: auto;
    height: calc(100vh - 200px);
    padding-right: 15px;

    .accordion-item {
      @include flex-content-space-between;
      margin-top: 15px;
      border-radius: 10px;
      padding: 12px 18px;
      background-color: $white-color;
  
      .plan-name {
        font-weight: 700;
        font-size: 17px;
        margin-bottom: 5px;
      }
  
      .plan-content {
        @include flex-content-align-center;
        font-size: 15px;
        font-weight: 600;
      }
  
      .plan-pricing {
        font-size: 16px;
        font-weight: 600;
        text-align: end;
      }
    }
  }

  .no-data-found-wrapper {
    text-align: center;
    padding: 30px 0;
    color: $gray-text;
  }
}

::ng-deep {
  .mat-drawer-content {
    overflow: hidden !important;
  }
  .sidenav-content-without-footer {
    overflow: hidden !important;
  }
}

@media (max-width: 767px) {
  .accordion-item {
    flex-wrap: wrap;

    .plan-content {
      flex-wrap: wrap;

      .dot {
        display: none;
      }
    }

    .plan-pricing {
      margin-top: 10px;
      width: 100%;
      text-align: left !important;
    }
  }
}
