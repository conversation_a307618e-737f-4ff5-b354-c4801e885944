@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.auth-page-with-header {
  height: calc(100vh - 105px);

  .o-card {
    padding: 0px;

    .o-card-body {
      .schedule-group-class-lesson-content-wrapper {
        overflow-x: hidden;
        border-radius: 12px;
        .content-detail-wrapper::-webkit-scrollbar {
          display: none;
        }

        .content-detail-wrapper {
          padding: 15px 35px;
          height: calc(100vh - 150px);
          overflow: auto;
          p {
            color: $gray-text;
            font-weight: 400;
            font-size: 16px;
          }

          .group-class-detail-wrapper {
            .schedule-information-title {
              font-size: 16px;
              font-weight: 700;
              padding-bottom: 10px;
            }

            .schedule-basic-details {
              background: $header-schedule-bg-color;
              padding: 12px 16px;
              margin: 8px 0;
              border-radius: 6px;

              .group-name-age {
                font-size: 16px;
                font-weight: 700;
              }

              .location,
              .instructor-name {
                @include flex-content-align-center;
                font-size: 14px;

                .location-icon,
                .instructor-icon {
                  height: 15px;
                  width: 15px;
                }

                .location-info-text,
                .instructor-name {
                  margin: 0 4px;

                  .name {
                    margin-left: 4px;
                    font-weight: 700;
                  }
                }
              }

              .location {
                margin: 3px 0;
              }
            }

            .schedule-dates-week-info {
              @include flex-content-align-center;
              margin: 8px 0;

              .schedule-dates-week-content {
                font-size: 15px;
                font-weight: 700;

                .week-info {
                  color: $gray-text;
                  font-weight: 500;
                }
              }
            }

            .info-icon {
              height: 24px;
              width: 20px;
              margin-right: 5px;
              filter: $gray-filter !important;
            }

            .student-capacity-schedule-time-info-wrapper {
              @include flex-content-align-center;
              font-size: 15px;
              font-weight: 700;
              margin: 8px 0;

              .info-icon {
                height: 18px;
                margin-right: 4px;
              }
            }

            .description {
              margin: 8px 0;
            }
          }
        }

        .schedule-group-class-info-form-wrapper {
          box-shadow: 0px 4px 50px 0px #0000000d;
          border-left: 1px solid $btn-options-border-color;
          height: 100%;

          .filters-main-wrapper {
            @include flex-content-align-center;
            padding: 15px 35px 0 35px;
            flex-wrap: wrap;

            .filter-wrapper {
              .filter-label {
                color: $gray-text;
              }

              ::ng-deep .filter-content {
                margin-right: 10px !important;

                mat-form-field {
                  border: 1px solid $btn-options-border-color;
                  border-radius: 6px;
                }

                .mdc-text-field {
                  background-color: $white-color !important;
                }

                .mat-mdc-form-field {
                  width: 100% !important;
                }
              }
            }
          }

          .group-classes-wrapper {
            padding: 15px 35px;
            height: calc(100vh - 230px) !important;
            overflow: auto;

            .group-class-content {
              border: 1px solid $btn-options-border-color;
              padding: 12px 16px;
              border-radius: 6px;
              margin-bottom: 10px;
              cursor: pointer;

              .group-class-info {
                @include flex-content-space-between;

                .name-price {
                  font-weight: 700;
                  font-size: 18px;
                }

                .date-time {
                  font-weight: 600;
                  font-size: 16px;
                }

                .repetition-week-capacity-instructor {
                  @include flex-content-align-center;
                  font-weight: 400;
                  font-size: 14px;
                  color: $gray-text;

                  .instructor-name {
                    margin-right: 5px;
                    font-weight: 600;
                  }
                }
              }
            }

            .selected-group-border {
              border: 2px solid $primary-color;
            }
          }

          .field-wrapper {
            padding: 15px 35px 0 35px;
            btn-typed-option-disabled {
              opacity: 0.5;
              cursor: not-allowed;
              pointer-events: none;
            }
            label {
              font-size: 16px;
              font-weight: 700;
              margin-bottom: 10px;
            }
          }
        }
      }
    }
  }
}

.disabled-btn {
  background-color: $btn-options-border-color !important;
}
.header-tab-with-btn {
  padding: 5px 10px 0px 25px !important;
}

::ng-deep {
  .select-box-options-wrapper {
    .mat-mdc-checkbox-checked {
      .mdc-label {
        color: $primary-color !important;
      }
    }
    .mdc-label {
      color: $black-shade-text !important;
    }
  }
}

@media (max-width: 990px) {
  .schedule-group-class-lesson-content-wrapper {
    .content-detail-wrapper {
      display: none;
    }

    .no-space-found-wrapper {
      height: 150px !important;
    }
    .schedule-group-class-info-form-wrapper {
      .filters-main-wrapper {
        flex-wrap: wrap;
        .filter-wrapper {
          margin-bottom: 5px;
        }
      }

      .group-classes-wrapper {
        height: calc(100vh - 375px) !important;
        overflow: auto;

        .group-class-content {
          .group-class-info {
            flex-wrap: wrap;

            .repetition-week-capacity-instructor {
              flex-wrap: wrap;
              .dot {
                display: none;
              }
            }
          }
        }
      }
    }
  }
}
