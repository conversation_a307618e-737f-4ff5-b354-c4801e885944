import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatNativeDateModule, provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { MatIconModule } from '@angular/material/icon';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatButtonModule } from '@angular/material/button';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBResponse } from 'src/app/shared/models';
import moment from 'moment';
import { SharedModule } from 'src/app/shared/shared.module';
import { MbscDatepickerModule } from '@mobiscroll/angular';
import { MatInputModule } from '@angular/material/input';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { AvailableLesson, InstructorBioDetail, IntroductoryLessonDetail, SelectedLessonInfo } from '../../models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { CommonService } from 'src/app/shared/services';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { Duration } from 'src/app/pages/settings/pages/plan/models';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';

const DEPENDENCIES = {
  MODULES: [
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    CommonModule,
    MatIconModule,
    OverlayModule,
    MatButtonModule,
    SharedModule,
    MbscDatepickerModule,
    MatInputModule,
    MatSidenavModule
  ],
  COMPONENTS: []
};

@Component({
  selector: 'app-available-introductory-lesson',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './available-introductory-lesson.component.html',
  styleUrl: './available-introductory-lesson.component.scss'
})
export class AvailableIntroductoryLessonComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() scheduleInfo!: SelectedLessonInfo;
  @Input() selectedInstructorsIdFromParent!: Array<number>;
  @Input() filteredInstructorCount!: number;
  @Input() isScheduleMakeUpLesson!: boolean;
  @Input() isPassReschedule!: boolean;

  isInstructorsSideNavOpen = false;
  showInstructorDetailsFlag = false;
  dateRange: { date: string }[] = [];
  appointments!: AvailableLesson[];
  currentDate = new Date();

  startDate!: Date;
  endDate!: Date;
  startTime!: Date;
  endTime!: Date;
  selectedDate!: string;
  selectedTime!: string;
  selectedTimeSlotId!: number;

  @Output() scheduleAppointmentsDetails = new EventEmitter<InstructorBioDetail>();
  @Output() toggleInstructorSideNav = new EventEmitter<boolean>();

  constructor(
    private readonly schedulerService: SchedulerService,
    private readonly datePipe: DatePipe,
    private readonly cdr: ChangeDetectorRef,
    private readonly commonService: CommonService
  ) {
    super();
  }

  ngOnInit(): void {
    console.log('🚀 AvailableIntroductoryLesson ngOnInit');
    console.log('📥 selectedInstructorsIdFromParent:', this.selectedInstructorsIdFromParent);
    console.log('📋 scheduleInfo:', this.scheduleInfo);
    console.log('📊 filteredInstructorCount:', this.filteredInstructorCount);
    this.initializeDates();
  }

  private lastApiCallParams: string = '';

  ngOnChanges(changes: SimpleChanges): void {
    console.log('🔄 AvailableIntroductoryLesson ngOnChanges:', Object.keys(changes));

    // Only call API if we have meaningful changes and both conditions are met
    let shouldCallAPI = false;

    // Check if instructor IDs actually changed
    if (changes['selectedInstructorsIdFromParent']) {
      const newIds = changes['selectedInstructorsIdFromParent'].currentValue;
      const oldIds = changes['selectedInstructorsIdFromParent'].previousValue;
      if (JSON.stringify(newIds) !== JSON.stringify(oldIds)) {
        console.log('🎯 Instructor IDs actually changed');
        console.log('📊 New count:', newIds?.length || 0);
        console.log('📊 Filtered count:', this.filteredInstructorCount);
        shouldCallAPI = true;
        // Trigger change detection to update the count display
        this.cdr.detectChanges();
      }
    }

    // Check if schedule info actually changed (compare key properties)
    if (changes['scheduleInfo']) {
      const newInfo = changes['scheduleInfo'].currentValue;
      const oldInfo = changes['scheduleInfo'].previousValue;

      if (!oldInfo ||
          newInfo?.locationId !== oldInfo?.locationId ||
          newInfo?.instrumentId !== oldInfo?.instrumentId ||
          newInfo?.duration !== oldInfo?.duration ||
          newInfo?.studentId !== oldInfo?.studentId) {
        console.log('📋 Schedule info actually changed');
        shouldCallAPI = true;
      }
    }

    // Call API only if there are meaningful changes and both conditions are met
    if (shouldCallAPI && this.selectedInstructorsIdFromParent?.length > 0 && this.scheduleInfo) {
      // Create a unique key for this API call to prevent duplicates
      const apiCallKey = JSON.stringify({
        instructorIds: this.selectedInstructorsIdFromParent,
        locationId: this.scheduleInfo.locationId,
        instrumentId: this.scheduleInfo.instrumentId,
        duration: this.scheduleInfo.duration,
        studentId: this.scheduleInfo.studentId,
        date: this.selectedDate
      });

      if (apiCallKey !== this.lastApiCallParams) {
        console.log('✅ Meaningful changes detected - calling API...');
        this.lastApiCallParams = apiCallKey;
        this.getIntroductoryLessonDetails();
      } else {
        console.log('🔄 Same parameters - skipping duplicate API call');
      }
    } else {
      console.log('⏸️ No meaningful changes or conditions not met');
    }
  }

  initializeDates(): void {
    console.log('📅 Initializing dates...');
    this.startDate = this.currentDate;
    this.endDate = this.getWeekEndDate();
    this.getDatesBetween(this.startDate, this.endDate);
    this.selectedDate = this.dateRange.length ? this.dateRange[0].date : new Date().toISOString();
    console.log('📅 Selected date set to:', this.selectedDate);
  }

  getIntroductoryLessonDetails(selectedInstructorsId?: Array<number>): void {
    console.log('🔍 Getting introductory lesson details...');

    const instructorIds = this.isPassReschedule
      ? [this.scheduleInfo.instructorId]
      : selectedInstructorsId ?? this.selectedInstructorsIdFromParent;

    console.log('🎯 Using instructor IDs:', instructorIds);

    // Ensure selectedDate is set to today if not already set
    const scheduleDate = this.selectedDate || new Date().toISOString();
    console.log('📅 Using schedule date:', scheduleDate);

    this.showPageLoader = true;
    this.schedulerService
      .add(
        {
          scheduleDate: this.datePipe.transform(scheduleDate, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss),
          locationId: this.scheduleInfo?.locationId,
          instrumentId: this.scheduleInfo?.instrumentId,
          duration: this.scheduleInfo?.duration ?? Duration.THIRTY,
          isIntroductoryClassAvailable: this.scheduleInfo.classType === ClassTypes.INTRODUCTORY,
          instructorIdFilter: instructorIds,
          startTimeFilter:
            this.datePipe.transform(this.startTime, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss) ?? null,
          endTimeFilter: this.datePipe.transform(this.endTime, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss) ?? null,
          studentId: this.scheduleInfo?.studentId
        },
        API_URL.scheduleLessonDetails.getAvailableIntroductoryLessons
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<AvailableLesson>) => {
          console.log('✅ Lesson details received:', res.result.items.length, 'appointments');
          this.appointments = res.result.items;
          this.setSelectedSlotIdAndShowInstructorDetailsFlag(
            this.appointments[0]?.introductoryLessonsDetails[0],
            false
          );
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('❌ Error getting lesson details:', error);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onDateRangeSelected(): void {
    if (this.startDate && this.endDate) {
      this.dateRange = [];
      this.getDatesBetween(this.startDate, this.endDate);
      this.selectedDate = this.dateRange[0].date;
      this.getIntroductoryLessonDetails();
    }
  }

  getDatesBetween(startDate: Date, endDate: Date): void {
    let currentDate = moment(startDate);
    const lastDate = moment(endDate);

    while (currentDate <= lastDate) {
      this.dateRange.push({
        date: currentDate.toString()
      });
      currentDate = currentDate.add(1, 'day');
    }
  }

  setTimeFilter(event: any): void {
    this.selectedTime = event.valueText;
    [this.startTime, this.endTime] = event.value;
    this.getIntroductoryLessonDetails();
  }

  setScheduleDate(date: string): void {
    this.selectedDate = date;
    this.getIntroductoryLessonDetails();
  }

  getWeekEndDate(): Date {
    return moment().add(6, 'days').toDate();
  }

  setSelectedSlotIdAndShowInstructorDetailsFlag(
    slotDetails: IntroductoryLessonDetail,
    flag: boolean,
    event?: MouseEvent
  ): void {
    if (flag) {
      event?.stopPropagation();
    }
    this.selectedTimeSlotId = slotDetails?.id;
    this.showInstructorDetailsFlag = flag;
    this.scheduleAppointmentsDetails.emit({
      slotDetails,
      showStaffDetails: flag,
      dateRange: { startDate: this.startDate, endDate: this.endDate }
    });
  }

  openInstructorSideNav(isOpen: boolean): void {
    console.log('🔄 Opening instructor side nav:', isOpen);
    this.toggleInstructorSideNav.emit(isOpen);
  }
}
