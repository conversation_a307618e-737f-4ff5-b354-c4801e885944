@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.o-sidebar-body {
  padding: 20px 30px !important;
  overflow: auto;
  height: calc(100vh - 56px);

  .cart-items-container {
    .cart-header {
      font-weight: 700;
      font-size: 18px;
      margin-bottom: 12px;
      color: $primary-color;
    }

    .cart-wrapper {
      background-color: $gray-bg-light;
      border-radius: 10px;
      padding: 20px;

      .cart-item {
        @include flex-content-space-between;

        .product-wrapper {
          @include flex-content-align-center;

          .product-image {
            border-radius: 5px;
            margin-right: 10px;
            height: 45px;
            width: 45px;
          }

          .index-number {
            margin-right: 12px;
            font-weight: 600;
          }
        }

        .plan-name {
          font-weight: 700;
          font-size: 17px;
          margin-bottom: 5px;
        }

        .plan-content {
          @include ellipse(400px);
          font-size: 15px;
          font-weight: 600;
          color: $gray-text;
        }

        .plan-pricing {
          font-size: 16px;
          font-weight: 600;
          text-align: end;
        }
      }
    }

    .payment-slip {
      margin-top: 20px;
      border-radius: 10px;
      padding: 12px 18px;
      background-color: $gray-bg-light;

      .payment-slip-item {
        @include flex-content-space-between;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .payment-slip-label {
          font-size: 16px;
          font-weight: 600;
          color: $gray-text;
        }

        .payment-slip-value {
          font-size: 16px;
          font-weight: 700;
        }
      }
    }
  }
}

::ng-deep {
  .mat-drawer-inner-container {
    overflow: hidden !important;
  }
  .sidenav-content-without-footer {
    overflow: hidden !important;
  }
}

@media (max-width: 767px) {
  .accordion-item {
    flex-wrap: wrap;

    .plan-content {
      flex-wrap: wrap;

      .dot {
        display: none;
      }
    }

    .plan-pricing {
      margin-top: 10px;
      width: 100%;
      text-align: left !important;
    }
  }
}
