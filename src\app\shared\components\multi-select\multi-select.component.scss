@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.select-box-wrapper {
  border: 1px solid $btn-options-border-color;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  width: 200px;
  margin-right: 10px;
  @include flex-content-space-between;

  mat-icon {
    color: $gray-text;
  }

  mat-icon {
    margin-top: 1px;
  }

  .selected-instructor-count {
    font-weight: 600;
  }
}

.select-box-options-wrapper {
  background: $white-color;
  box-shadow: 0px 0px 30px 0px #0000001a;
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 14px;
  width: 200px;
  max-height: 230px;
  overflow: auto;

  .option-img {
    height: 18px;
    width: 10px;
    background-color: $dot-dark-gray-color;
  }

  .margin-left {
    margin-left: 10px;
  }

  .option-wrapper .mat-mdc-checkbox {
    padding: 8px;
  }
}

::ng-deep {
  .mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
    border-color: $primary-color !important;
    background-color: #ffffff !important;
  }

  .mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background .mdc-checkbox__checkmark {
    color: $primary-color !important;
  }

  .mdc-checkbox__checkmark {
    top: 3px !important;
    height: 10px;
  }

  .mat-mdc-checkbox .mdc-checkbox__background {
    border-radius: 6px;
    border-width: 1px !important;
  }

  .mdc-label {
    color: $black-color;
    font-size: 14px;
    font-weight: 400;
  }

  .mat-mdc-checkbox-checked {
    .mdc-label {
      color: $primary-color !important;
    }
    color: $primary-color;
  }

  .search-bar-wrapper-instructor .mat-mdc-form-field-flex,
  .search-bar-wrapper-instructor .mat-mdc-form-field-focus-overlay,
  .search-bar-wrapper-instructor .mat-mdc-text-field-wrapper {
    height: 30px !important;
    padding: 0px 5px !important;
    font-size: 14px !important;
  }

  .search-bar-wrapper-instructor {
    .mdc-text-field {
      background-color: $white-color !important;
      border: 1px solid $btn-options-border-color;
    }
  }
}

@media (max-width: 530px) {
  .select-box-wrapper {
    margin-bottom: 5px;
  }
}
