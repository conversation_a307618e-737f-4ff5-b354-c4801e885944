@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

::ng-deep {
  .select-box-options-wrapper {
    .mat-mdc-checkbox-checked {
      .mdc-label {
        color: $primary-color !important;
      }
    }
    .mdc-label {
      color: $black-shade-text !important;
    }
  }
}


.auth-page-with-header {
  overflow: hidden !important;
  height: calc(100vh - 160px) !important;

  &.hide-pagination {
    height: calc(100vh - 107px) !important;
  }

  .search-and-count-wrapper-auth {
    justify-content: space-between;

    .search-and-count-wrapper {
      @include flex-content-align-center;
    }
  }

  ::ng-deep {
    .mdc-text-field {
      background-color: $white-color !important;
    }
    .mat-mdc-form-field {
      width: 90% !important;
    }
    .mat-mdc-select-value-text {
      font-size: 14px;
      color: $black-color;
    }
    .select-box-wrapper {
      background: $white-color;
      border: none !important;
    }
  }

  .no-data-found-card {
    height: calc(100vh - 220px) !important;
  }

  .summer-camp-list {
    overflow: auto;
    height: calc(100vh - 220px);
    padding-right: 10px;

    &.hide-pagination {
      height: calc(100vh - 220px) !important;
    }

    .o-card {
      padding: 14px 20px !important;

      .o-card-body {
        @include flex-content-space-between;

        .title {
          font-size: 16px;
          font-weight: 700;
          margin-bottom: 7px;
        }

        .summer-camp-content {
          @include flex-content-align-center;

          .dot {
            margin: 0px 6px !important;
          }

          div {
            font-size: 14px;
            font-weight: 600;
          }
        }

        .summer-camp-pricing {
          @include flex-content-space-between;

          img {
            padding: 0px 8px;
          }
        }
      }
    }
  }
}

.pagination-controls {
  margin-top: 10px !important;
  margin-right: 10px;
}

@media (max-width: 820px) {
  .auth-page-with-header {
    .mat-mdc-form-field {
      width: 87% !important;
      margin-bottom: 5px;
    }

    .search-and-count-wrapper-auth {
      display: block;

      .search-and-count-wrapper {
        display: block !important;
        margin-bottom: 10px;

        .total-users {
          margin-bottom: 10px;

          span {
            border: none !important;
          }
        }
      }
    }

    .summer-camp-list {
      height: calc(100vh - 285px) !important;

      .o-card {
        .o-card-body,
        .summer-camp-content {
          flex-direction: column;
          align-items: flex-start !important;

          .dot {
            display: none;
          }

          .title {
            text-wrap: balance;
            margin-bottom: 0px !important;
          }
        }
      }
    }
  }
}

@media (max-width: 1115px) and (min-width: 819px) {
  .auth-page-with-header {
    .search-and-count-wrapper-auth {
      display: block;

      .search-and-count-wrapper {
        display: block !important;
        margin-bottom: 10px;

        .total-users {
          margin-bottom: 10px;

          span {
            border: none !important;
          }
        }
      }
    }

    .summer-camp-list {
      height: calc(100vh - 272px) !important;
    }
  }
}

::ng-deep {
  .mat-drawer-content {
    overflow: hidden !important;
  }
  .sidenav-content-without-footer {
    overflow: hidden !important;
  }
}
