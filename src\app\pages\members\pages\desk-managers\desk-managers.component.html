<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    [ngClass]="isAddDeskManager ? 'sidebar-w-850' : 'lg-sidebar'"
    [disableClose]="true">
    <ng-container [ngTemplateOutlet]="isAddDeskManager ? addDeskManager : viewDeskManager"></ng-container>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-only-btn">
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn action-btn"
        type="button"
        (click)="openAddOrViewDeskManager(true, null)">
        Add Desk Manager
      </button>
    </div>

    <div class="auth-page-wrapper auth-page-with-header">
      <div class="filter-and-count-wrapper mb-2">
        <div class="search-and-count-wrapper-auth">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input
                matInput
                placeholder="Search.."
                [(ngModel)]="filters.searchTerm"
                (ngModelChange)="onSearchTermChanged()" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <div class="filter-wrapper">
          <div class="search-bar-wrapper">
            <app-multi-select
              [filterDetail]="filters.locationId"
              (selectedFilterValues)="getDeskManagers((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>
        </div>
      </div>

      <div class="o-card">
        <div class="o-card-body">
          <div class="o-table">
            <div class="o-row o-header">
              <div class="o-cell first-cell">Desk Manager</div>
              <div class="o-cell">Email</div>
              <div class="o-cell">Phone No.</div>
              <div class="o-cell">Location</div>
            </div>
            <div class="dotted-divider"></div>
            <div class="content" [ngClass]="totalCount > 10 ? 'show-pagination' : 'hide-pagination'">
              <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : instructorTemplate"></ng-container>
            </div>
          </div>
        </div>
      </div>
      @if (totalCount > 10) {
        <pagination-controls
          id="deskManager"
          [previousLabel]="''"
          [nextLabel]="''"
          (pageChange)="onPageChange($event)"
          [responsive]="true"
          class="pagination-controls"></pagination-controls>
      }
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #addDeskManager>
  <app-add-desk-manager
    [selectedDeskManagerDetails]="selectedDeskManagerViewDetails"
    (closeSideNav)="closeAddOrEditDeskManager($event)"
    (isInstructorAdded)="getDeskManagers(currentPage, pageSize)"></app-add-desk-manager>
</ng-template>

<ng-template #viewDeskManager>
  <app-view-desk-manager
    [selectedDeskManagerViewDetails]="selectedDeskManagerViewDetails"
    (openEditSideNav)="openAddOrViewDeskManager(true, selectedDeskManagerViewDetails)"
    (closeViewSideNav)="closeAddOrEditDeskManager(null)"></app-view-desk-manager>
</ng-template>

<ng-template #instructorTemplate>
  <ng-container [ngTemplateOutlet]="totalCount ? deskManagerTableContent : noDataFound"></ng-container>
</ng-template>

<ng-template #deskManagerTableContent>
  @for (
    deskManager of deskManagers
      | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "deskManager" };
    track $index
  ) {
    <div class="o-row">
      <div
        class="o-cell first-cell instructor-name-photo-wrapper"
        (click)="openAddOrViewDeskManager(false, deskManager.deskManagerDetails)">
        @if (deskManager.deskManagerDetails.profilePic) {
          <img [src]="deskManager.deskManagerDetails.profilePic" class="me-2" alt="" />
        } @else {
          <div class="placeholder-name">
            <div>
              {{ getInitials(deskManager.deskManagerDetails.name) | uppercase }}
            </div>
          </div>
        }
        <div class="text-truncate" [matTooltip]="deskManager.deskManagerDetails.name">{{ deskManager.deskManagerDetails.name | titlecase }}</div>
      </div>
      <div class="o-cell text-gray text-truncate" [matTooltip]="deskManager.deskManagerDetails.email">{{ deskManager.deskManagerDetails.email }}</div>
      <div class="o-cell text-gray text-truncate" [matTooltip]="deskManager.deskManagerDetails.phoneNumber">{{ deskManager.deskManagerDetails.phoneNumber }}</div>
      <div class="o-cell text-gray">
        <div class="instrument-wrapper">
          @if (deskManager.deskManagerDetails.deskManagerAvailabilityAndLocations.length) {
            <img [src]="constants.staticImages.icons.location" class="me-1" alt="" />
            @for (
              instructorAvailability of deskManager.deskManagerDetails.deskManagerAvailabilityAndLocations;
              track $index
            ) {
              <div class="instrument-item" *ngIf="$index < 1">{{ instructorAvailability.locationName }}</div>
              <div
                class="dot"
                *ngIf="
                  $index < 1 &&
                  getLocationDetails(deskManager.deskManagerDetails.deskManagerAvailabilityAndLocations).count - 1 !==
                    $index
                "></div>
            }
            @if (getLocationDetails(deskManager.deskManagerDetails.deskManagerAvailabilityAndLocations).count > 1) {
              <div
                class="remaining-instrument-available-count"
                [matTooltip]="
                  getLocationDetails(deskManager.deskManagerDetails.deskManagerAvailabilityAndLocations).names
                ">
                {{ getLocationDetails(deskManager.deskManagerDetails.deskManagerAvailabilityAndLocations).count - 1 }}+
              </div>
            }
          } @else {
            -
          }
        </div>
      </div>
    </div>

    @if ($index < deskManagers.length - 1) {
      <div class="dotted-divider"></div>
    }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
