@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.schedule-wrapper {
  .calender-action-filter-wrapper {
    margin-bottom: 10px;
    @include flex-content-space-between;

    .calender-actions {
      @include flex-content-align-center;
      font-size: 13px;

      .today-btn {
        padding: 4px 14px;
      }

      .action-border {
        border: 1px solid $btn-options-border-color;
        border-radius: 4px;
        color: $gray-text;
        cursor: pointer;
      }

      .calender-icon {
        margin: 0 8px;
        padding: 4px 5px;

        img {
          filter: $gray-filter;
        }
      }

      .current-date {
        font-weight: 700;
      }

      .change-date-pre-next-wrapper {
        @include flex-content-align-center;
        margin: 0 10px;

        .prev-next-icon-wrapper {
          padding: 5px;

          mat-icon {
            height: 16px;
            width: 13px;
            font-size: 16px !important;
            color: $gray-text !important;
            @include flex-content-center;
          }
        }

        .datepicker {
          width: 1px;
          height: 1px;
          visibility: hidden;
        }
      }
    }
  }

  .filters-wrapper {
    margin-bottom: 10px;
    display: flex;
  }

  .room-scheduler-wrapper {
    height: calc(100vh - 305px);
    border: 1px solid $btn-options-border-color;
  }
}

.room-scheduler-details-wrapper {
  padding: 15px;

  .name {
    font-size: 15px;
    font-weight: 700;
  }

  .event-info-wrapper,
  .room-scheduler-header {
    padding-bottom: 5px;

    .event-info-content,
    .room-info-content {
      @include flex-content-align-center;
      padding: 2px 0;

      img {
        height: 16px;
        width: 14px;
        margin-right: 5px;
        filter: $primary-color-filter;
      }

      .gray-text {
        filter: $gray-filter;
        color: $gray-text !important;
      }

      .info-text {
        font-size: 14px;
        color: $original-black-color;

        .instruments-wrapper {
          @include flex-content-align-center;
          flex-wrap: wrap;

          .dot {
            margin: 0px 5px;
          }
        }
      }
    }

    .room-info-content {
      img {
        filter: $gray-filter !important;
      }

      .room-info-text {
        font-size: 14px;
        color: $gray-text !important;

        .instruments-wrapper {
          @include flex-content-align-center;
          flex-wrap: wrap;

          .dot {
            margin: 0px 5px;
          }
        }
      }
    }
  }

  ::ng-deep .action-btn-wrapper {
    text-align: center;
    margin-top: 5px;

    .action-btn {
      min-width: 145px;
      height: 35px !important;
    }

    .mat-primary-btn .mat-mdc-focus-indicator,
    .mat-red-btn .mat-mdc-focus-indicator {
      height: 35px !important;
    }
  }
}

::ng-deep {
  .mbsc-ios.mbsc-eventcalendar .mbsc-calendar-wrapper {
    display: none;
  }

  .room-scheduler.mbsc-calendar .mbsc-schedule-event-all-day-inner {
    height: 40px;
    display: flex;
    align-items: center;
  }

  .room-scheduler {
    .mbsc-timeline-resource.mbsc-ltr {
      background: $resource-cell-color;

      .mbsc-timeline-resource-title {
        height: 100%;
      }
    }

    .mbsc-timeline-resource-header-cont.mbsc-ltr {
      background: $resource-cell-color;
    }

    .room-scheduler-date-header-wrapper {
      padding: 10px;
      background: $resource-cell-color;

      .room-scheduler-day {
        color: $dot-dark-gray-color;
        font-weight: 600;
        text-align: center;
      }
    }

    .room-info-wrapper {
      @include flex-content-center;
      height: inherit;
      cursor: pointer;

      .room-name {
        font-weight: 700;
      }
    }

    .room-scheduler-item-wrapper {
      height: 40px;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      display: flex;

      .schedule-border {
        width: 3px;
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }

      .scheduler-info-wrapper {
        padding: 3px 5px;
        width: -webkit-fill-available;

        .instructor-name-action-wrapper {
          @include flex-content-space-between;
          .instructor-name {
            @include ellipse(115px);
            font-weight: 600;
            font-size: 14px;
          }

          mat-icon {
            font-size: 14px !important;
            height: auto;
            width: auto;
          }
        }

        .schedule-time-info {
          font-weight: 400;
          color: $black-color;
          font-size: 12px;
          @include ellipse(115px);
        }
      }
    }

    .mbsc-calendar .mbsc-schedule-event-all-day-inner,
    .mbsc-timeline-event .mbsc-schedule-event-title {
      width: 100%;
    }

    .mbsc-calendar .mbsc-schedule-event-all-day-inner,
    .mbsc-timeline-event .mbsc-schedule-event-title {
      padding: 0 !important;
    }
  }
  .location-filter-wrapper {
    margin-right: 10px !important;
    width: 200px !important;

    mat-form-field {
      border: 1px solid #d5d5d5;
      border-radius: 6px;
    }

    .mdc-text-field {
      background-color: $white-color !important;
    }

    .mat-mdc-form-field {
      width: 100% !important;
    }
  }
}

@media (max-width: 530px) {
  .calender-action-filter-wrapper {
    flex-wrap: wrap;

    .calender-actions {
      flex-wrap: wrap;
      margin-bottom: 5px;

      .current-date {
        margin-top: 5px;
      }
    }
  }

  .filters-wrapper {
    flex-wrap: wrap;
  }

  ::ng-deep {
    .mbsc-timeline-resource-bg,
    .mbsc-timeline-resource-header-cont.mbsc-ltr,
    .mbsc-has-sticky .mbsc-timeline-resources.mbsc-ltr,
    .mbsc-has-sticky .mbsc-timeline-sidebar.mbsc-ltr {
      width: 100px;
    }
  }
}
