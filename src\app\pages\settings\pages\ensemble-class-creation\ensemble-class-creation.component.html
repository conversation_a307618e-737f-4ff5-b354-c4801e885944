<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isAddEsembleClassSideNavOpen || isEnrolSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    [ngClass]="isEnrolSideNavOpen ? 'md-sidebar' : 'sidebar-w-750'"
    [disableClose]="true"
  >
    <ng-container [ngTemplateOutlet]="selectedEnsembleId ? updateOrViewEnsembleClass : addEnsembleClass"></ng-container>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-with-btn">
      <div class="tab-item-content">
        @for (pageTabOption of pageTabOptions | keyvalue: keepOriginalOrder; track $index) {
        <div
          [ngClass]="{ item: true, 'active-item': selectedTabOption === pageTabOption.value }"
          (click)="setActiveTabOption(pageTabOption.value)"
        >
          {{ pageTabOption.value }}
        </div>
        }
      </div>
      <div class="action-btn-wrapper">
        <button
          mat-raised-button
          color="primary"
          class="mat-primary-btn action-btn"
          type="button"
          (click)="isAddEsembleClassSideNavOpen = true"
        >
          Add Ensemble Class
        </button>
      </div>
    </div>

    <div class="auth-page-with-header" [ngClass]="{ 'hide-pagination': totalCount < 11 }">
      <div class="search-and-count-wrapper-auth">
        <div class="search-and-count-wrapper">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
        </div>
        <div class="filter-wrapper">
          <div class="search-bar-wrapper main">
            <app-multi-select
              [filterDetail]="filters.locationIdFilter"
              (selectedFilterValues)="getEnsembleClassDetail((currentPage = 1), pageSize)"
            ></app-multi-select>
          </div>
          <div class="search-bar-wrapper main">
            <app-multi-select
              [filterDetail]="filters.instrumentIdFilter"
              (selectedFilterValues)="getEnsembleClassDetail((currentPage = 1), pageSize)"
            ></app-multi-select>
          </div>
          <mat-form-field class="search-bar-wrapper">
            <mat-select [(ngModel)]="filters.ageGroupFilter" (selectionChange)="getEnsembleClassDetail((currentPage = 1), pageSize)">
              <mat-option [value]="all.ALL">All Age Group</mat-option>
              <mat-option *ngFor="let age of constants.ageOptions" [value]="age.value"> {{ age.label }} Age Group </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : ensembleClassLists"></ng-container>
    </div>
    @if (totalCount > 10) {
    <pagination-controls
      id="ensemble-class"
      [previousLabel]="''"
      [nextLabel]="''"
      (pageChange)="onPageChange($event)"
      [responsive]="true"
      class="pagination-controls"
    ></pagination-controls>
    }
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #addEnsembleClass>
  @if (isEnrolSideNavOpen) {
    <app-enroll-student-in-ensemble
      [selectedClass]="selectedEnsembleClass"
      (refreshScheduleData)="getEnsembleClassDetail((currentPage = 1), pageSize)"
      (closeSideNav)="toggleEnrollStudentSideNav(false, null)"
    ></app-enroll-student-in-ensemble>
  }
  @else {
    <app-add-ensemble-class
      (closeSideNav)="isAddEsembleClassSideNavOpen = false"
      (isEnsembleClassAdded)="getEnsembleClassDetail(currentPage, pageSize)"
    ></app-add-ensemble-class>
  }
</ng-template>

<ng-template #updateOrViewEnsembleClass>
  @if (isViewEnsembleClassOpen) {
  <app-view-ensemble-class
    (closeViewSideNav)="toggleAddEditSideNav(false, false, null)"
    (EnsembleClassUpdated)="getEnsembleClassDetail(currentPage, pageSize)"
    (openEditSideNav)="toggleAddEditSideNav(true, false, selectedEnsembleId, true)"
    [selectedTabOption]="selectedTabOption"
    [selectedEnsembleId]="selectedEnsembleId"
  ></app-view-ensemble-class>
  } @else {
  <app-update-ensemble-class
    (closeSideNav)="
      isCloneSideNavOpen = false; toggleAddEditSideNav(isEditFromView, isEditFromView, isEditFromView ? selectedEnsembleId : null)
    "
    (isEnsembleClassUpdated)="getEnsembleClassDetail(currentPage, pageSize)"
    [isCloneSideNavOpen]="isCloneSideNavOpen"
    [selectedEnsembleId]="selectedEnsembleId"
  ></app-update-ensemble-class>
  }
</ng-template>

<ng-template #ensembleClassLists>
  <ng-container [ngTemplateOutlet]=" ensembleClassDetails && ensembleClassDetails.length ? ensembleClassList : noDataFound"></ng-container>
</ng-template>

<ng-template #ensembleClassList>
  <div class="ensemble-class-list" [ngClass]="{ 'hide-pagination': totalCount < 11 }">
    @for ( ensembleClassDetail of ensembleClassDetails | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems:
    totalCount, id: "ensemble-class" }; track $index ) {

    <div class="o-card mb-2">
      <div class="o-card-body">
        <div class="pointer" (click)="toggleAddEditSideNav(true, true, ensembleClassDetail.id)">
          <div class="title">
            {{ ensembleClassDetail.ensembleClassName | titlecase }}
          </div>
          <div class="ensemble-class-content">
            <div class="text-black">
              {{ ensembleClassDetail.scheduleStartDate | date : 'mediumDate' }}
            </div>
            <div class="dot"></div>
            <div class="text-black">
              {{ ensembleClassDetail.scheduleStartTime | date : 'shortTime' }}
              - {{ ensembleClassDetail.scheduleEndTime | date : 'shortTime' }}
            </div>
            <div class="dot"></div>
            <div>
              Every {{ schedulerService.getDayOfWeek(ensembleClassDetail.daysOfSchedule) }} for
              <span class="primary-color"
                >{{
                  schedulerService.getNumberOfWeeks(ensembleClassDetail.scheduleStartDate, ensembleClassDetail.scheduleEndDate)
                }}
                Weeks</span
              >
            </div>
            <div class="dot"></div>
            <div>
              {{ ensembleClassDetail.locationName }}
            </div>
          </div>
        </div>
        <div class="ensemble-class-pricing">
          <!-- to be used -->
          <!-- <img alt=""
            class="pointer"
            matTooltip="Enroll Students"
            [src]="constants.staticImages.icons.memberIcon"
            (click)="toggleEnrollStudentSideNav(true, ensembleClassDetail)"
          /> -->
          <img
            *ngIf="selectedTabOption === pageTabOptions.UPCOMING"
            class="pointer" matTooltip="Edit" alt=""
            [src]="constants.staticImages.icons.editPenGray"
            (click)="isCloneSideNavOpen = false; toggleAddEditSideNav(true, false, ensembleClassDetail.id)"
          />
          <img
            class="pointer" matTooltip="Clone" alt=""
            [src]="constants.staticImages.icons.clone"
            (click)="isCloneSideNavOpen = true; toggleAddEditSideNav(true, false, ensembleClassDetail.id)"
          />
          <img
            class="pointer" alt=""
            height="22" matTooltip="Delete"
            [src]="constants.staticImages.icons.trash"
            (click)="deleteEnsembleClassConfirmation(ensembleClassDetail.id)"
          />
        </div>
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <app-content-loader></app-content-loader>
</ng-template>
