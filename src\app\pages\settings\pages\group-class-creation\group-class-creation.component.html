<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isAddGroupClassSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-750"
    [disableClose]="true">
    <ng-container [ngTemplateOutlet]="selectedGroupId ? updateOrViewGroupClass : addGroupClass"></ng-container>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-with-btn">
      <div class="tab-item-content">
        @for (pageTabOption of pageTabOptions | keyvalue: keepOriginalOrder; track $index) {
          <div
            [ngClass]="{ item: true, 'active-item': selectedTabOption === pageTabOption.value }"
            (click)="setActiveTabOption(pageTabOption.value)">
            {{ pageTabOption.value }}
          </div>
        }
      </div>
      <div class="action-btn-wrapper">
        <button
          mat-raised-button
          color="primary"
          class="mat-primary-btn action-btn"
          type="button"
          (click)="isAddGroupClassSideNavOpen = true">
          Add Group Class
        </button>
      </div>
    </div>

    <div class="auth-page-with-header" [ngClass]="{ 'hide-pagination': totalCount < 11 || !totalCount }">
      <div class="search-and-count-wrapper-auth">
        <div class="search-and-count-wrapper">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
        </div>
        <div class="filter-wrapper">
          <div class="search-bar-wrapper">
            <app-multi-select
              [filterDetail]="filters.instrumentIdFilter"
              (selectedFilterValues)="getGroupClassDetail((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>
          <mat-form-field class="search-bar-wrapper">
            <mat-select
              [(ngModel)]="filters.ageGroupFilter"
              (selectionChange)="getGroupClassDetail((currentPage = 1), pageSize)">
              <mat-option [value]="all.ALL">All Age Group</mat-option>
              <mat-option *ngFor="let age of constants.ageOptions" [value]="age.value">
                {{ age.label }} Age Group
              </mat-option>
            </mat-select>
          </mat-form-field>
          <div class="search-bar-wrapper">
            <app-multi-select
              [filterDetail]="filters.locationIdFilter"
              (selectedFilterValues)="getGroupClassDetail((currentPage = 1), pageSize)">
            </app-multi-select>
          </div>
        </div>
      </div>
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : groupClassLists"></ng-container>
    </div>
    @if (totalCount > 10) {
      <pagination-controls
        id="group-class"
        [previousLabel]="''"
        [nextLabel]="''"
        (pageChange)="onPageChange($event)"
        [responsive]="true"
        class="pagination-controls"></pagination-controls>
    }
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #addGroupClass>
  <app-add-group-class
    (closeSideNav)="isAddGroupClassSideNavOpen = false"
    (isGroupClassAdded)="getGroupClassDetail(currentPage, pageSize)"></app-add-group-class>
</ng-template>

<ng-template #updateOrViewGroupClass>
  @if (isViewGroupClassOpen) {
    <app-view-group-class
      (closeViewSideNav)="toggleAddEditSideNav(false, false, null)"
      (groupClassUpdated)="getGroupClassDetail(currentPage, pageSize)"
      (openEditSideNav)="toggleAddEditSideNav(true, false, selectedGroupId, true)"
      [selectedTabOption]="selectedTabOption"
      [selectedGroupId]="selectedGroupId"></app-view-group-class>
  } @else {
    <app-update-group-class
      (closeSideNav)="
        isCloneSideNavOpen = false;
        toggleAddEditSideNav(isEditFromView, isEditFromView, isEditFromView ? selectedGroupId : null)
      "
      (isGroupClassUpdated)="getGroupClassDetail(currentPage, pageSize)"
      [isCloneSideNavOpen]="isCloneSideNavOpen"
      [selectedGroupId]="selectedGroupId"></app-update-group-class>
  }
</ng-template>

<ng-template #groupClassLists>
  <ng-container [ngTemplateOutlet]="groupClassDetails.length ? groupClassList : noDataFound"></ng-container>
</ng-template>

<ng-template #groupClassList>
  <div class="group-class-list" [ngClass]="{ 'hide-pagination': totalCount < 11 }">
    @for (
      groupClassDetail of groupClassDetails
        | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "group-class" };
      track $index
    ) {
      <div class="o-card mb-2">
        <div class="o-card-body">
          <div
            class="pointer"
            (click)="toggleAddEditSideNav(true, true, groupClassDetail.groupClassScheduleSummary.id)">
            <div class="title">
              {{ groupClassDetail.groupClassScheduleSummary.groupClassName | titlecase }}
              ({{ schedulerService.getAgeLabelFromValue(groupClassDetail.groupClassScheduleSummary.ageGroup) }})
            </div>
            <div class="group-class-content">
              <div class="text-black">
                {{ groupClassDetail.groupClassScheduleSummary.scheduleStartDate | date: "mediumDate" }}
                - {{ groupClassDetail.groupClassScheduleSummary.scheduleEndDate | date: "mediumDate" }}
              </div>
              <div class="dot"></div>
              <div class="text-black">
                {{ groupClassDetail.groupClassScheduleSummary.scheduleStartTime | date: "shortTime" }}
                - {{ groupClassDetail.groupClassScheduleSummary.scheduleEndTime | date: "shortTime" }}
              </div>
              <div class="dot"></div>
              <div>
                Every {{ schedulerService.getDayOfWeek(groupClassDetail.groupClassScheduleSummary.scheduleDays) }} for
                <span class="primary-color"
                  >{{
                    schedulerService.getNumberOfWeeks(
                      groupClassDetail.groupClassScheduleSummary.scheduleStartDate,
                      groupClassDetail.groupClassScheduleSummary.scheduleEndDate
                    )
                  }}
                  Weeks</span
                >
              </div>
              <div class="dot"></div>
              <div>
                {{ groupClassDetail.groupClassScheduleSummary.locationName }}
              </div>
            </div>
          </div>
          <div class="group-class-pricing">
            <div class="title mb-0 pe-2">${{ groupClassDetail.groupClassScheduleSummary.price }}</div>
            <img
              *ngIf="selectedTabOption === pageTabOptions.UPCOMING"
              class="pointer" alt=""
              [src]="constants.staticImages.icons.editPenGray"
              (click)="
                isCloneSideNavOpen = false;
                toggleAddEditSideNav(true, false, groupClassDetail.groupClassScheduleSummary.id)
              " />
            <img
              class="pointer" alt=""
              [src]="constants.staticImages.icons.clone"
              (click)="
                isCloneSideNavOpen = true;
                toggleAddEditSideNav(true, false, groupClassDetail.groupClassScheduleSummary.id)
              " />
            <img
              class="pointer"
              height="22" alt=""
              [src]="constants.staticImages.icons.trash"
              (click)="deleteGroupClassConfirmation(groupClassDetail.groupClassScheduleSummary.id)" />
          </div>
        </div>
      </div>
    }
  </div>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <app-content-loader></app-content-loader>
</ng-template>
