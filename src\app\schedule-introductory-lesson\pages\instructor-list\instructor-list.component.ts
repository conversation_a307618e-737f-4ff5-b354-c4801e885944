import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { InstructorService } from '../../services';
import { takeUntil } from 'rxjs';
import { CBResponse } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { InstructorList } from '../../models';
import { FormsModule } from '@angular/forms';
import { Debounce } from 'src/app/shared/decorators';
import { CommonUtils } from 'src/app/shared/utils';
import { SupervisorFilter } from 'src/app/pages/members/pages/supervisors/models';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    MatButtonModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    FormsModule
  ],
  COMPONENTS: []
};

@Component({
  selector: 'app-instructor-list',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './instructor-list.component.html',
  styleUrl: './instructor-list.component.scss'
})
export class InstructorListComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedInstructorsIdFromParent!: Array<number>;
  @Input() selectedLocationId!: number | undefined;
  @Input() selectedInstrumentId!: number | undefined;

  readMoreTextIndex!: number | undefined;
  searchTerm!: string;
  selectedInstructorsId!: Array<number>;
  instructors!: Array<InstructorList>;

  showFullDescription = false;
  isAllInstructorSelected = false;

  @Output() closeInstructorSidNav = new EventEmitter<boolean>();
  @Output() resetInstructorSideNav = new EventEmitter<void>();
  @Output() setSelectedInstructorId = new EventEmitter<Array<number>>();

  constructor(
    private readonly instructorService: InstructorService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    console.log('🚀 InstructorList ngOnInit');
    console.log('📥 selectedInstructorsIdFromParent:', this.selectedInstructorsIdFromParent);
    this.selectedInstructorsId = this.selectedInstructorsIdFromParent || [];
    console.log('📝 Initial selectedInstructorsId:', this.selectedInstructorsId);
    this.getInstructors(false, true); // Pass true for isInitialLoad
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('🔄 InstructorList ngOnChanges:', changes);
    if (changes['selectedInstructorsIdFromParent']?.currentValue) {
      console.log('📥 New selectedInstructorsIdFromParent:', changes['selectedInstructorsIdFromParent'].currentValue);
      this.selectedInstructorsId = changes['selectedInstructorsIdFromParent'].currentValue;
    }
    if (changes['selectedLocationId']?.currentValue) {
      console.log('📍 Location changed:', changes['selectedLocationId'].currentValue);
      this.selectedLocationId = changes['selectedLocationId'].currentValue;
      this.getInstructors();
    }
    if (changes['selectedInstrumentId']?.currentValue) {
      console.log('🎵 Instrument changed:', changes['selectedInstrumentId'].currentValue);
      this.selectedInstrumentId = changes['selectedInstrumentId'].currentValue;
      this.getInstructors();
    }
  }

  getFilterParamsForInstructors() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : null,
      isSupervisorFilter: SupervisorFilter.ALL,
      NameFilter: this.searchTerm,
      LocationIdFilter: [this.selectedLocationId],
      InstrumentIdFilter: [this.selectedInstrumentId],
      Page: 1
    });
  }

  getInstructors(isSearching = false, isInitialLoad = false): void {
    console.log('🔍 Getting instructors, isSearching:', isSearching, 'isInitialLoad:', isInitialLoad);
    this.instructorService
      .add(this.getFilterParamsForInstructors(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: CBResponse<InstructorList>) => {
          console.log('📋 Instructors received:', response.result.items.length, 'Total count:', response.result.totalCount);
          this.instructors = response.result.items;

          // Check if all instructors are selected to update the checkbox state
          if (this.selectedInstructorsId.length === this.instructors.length) {
            this.isAllInstructorSelected = true;
          } else {
            this.isAllInstructorSelected = false;
          }

          this.cdr.detectChanges();
        }
      });
  }

  @Debounce()
  onSearchTermChanged(): void {
    this.getInstructors(true);
  }

  closeInstructorSideNavFn(): void {
    console.log('🚪 Closing instructor side nav without selection');
    this.resetInstructorSideNav.emit();
    this.closeInstructorSidNav.emit(false);
  }

  selectInstructorsAndClose(): void {
    console.log('✅ Select button clicked - sending selection to parent and closing');
    console.log('📤 Final selected instructors:', this.selectedInstructorsId);
    console.log('📊 Total selected count:', this.selectedInstructorsId.length);
    console.log('📊 Total available instructors:', this.instructors.length);

    // Emit the final selection to parent
    console.log('📤 Emitting to parent...');
    this.setSelectedInstructorId.emit(this.selectedInstructorsId);

    // Close the sidenav
    console.log('🚪 Closing sidenav...');
    this.closeInstructorSidNav.emit(false);
  }

  selectUnselectAllInstructors(): void {
    console.log('🔄 selectUnselectAllInstructors called');
    console.log('📊 Current selection length:', this.selectedInstructorsId.length);
    console.log('📊 Total instructors:', this.instructors.length);

    if (this.selectedInstructorsId.length === this.instructors.length) {
      console.log('❌ Unselecting all instructors');
      this.selectedInstructorsId = [];
      this.isAllInstructorSelected = false;
    } else {
      console.log('✅ Selecting all instructors');
      this.selectedInstructorsId = this.instructors.map((instructor) => instructor.instructorDetail.id);
      this.isAllInstructorSelected = true;
    }
    console.log('📝 Final selection:', this.selectedInstructorsId);
    // Don't emit immediately - wait for "Select" button click
  }

  selectInstructor(id: number): void {
    console.log('🎯 Selecting/deselecting instructor:', id);
    if (this.selectedInstructorsId.includes(id)) {
      console.log('❌ Removing instructor from selection');
      this.selectedInstructorsId = this.selectedInstructorsId.filter((instructorId) => instructorId !== id);
      this.isAllInstructorSelected = false;
    } else {
      console.log('✅ Adding instructor to selection');
      this.selectedInstructorsId.push(id);
      // Check if all instructors are now selected
      if (this.selectedInstructorsId.length === this.instructors.length) {
        this.isAllInstructorSelected = true;
      }
    }
    console.log('📝 Updated selection:', this.selectedInstructorsId);
    // Don't emit immediately - wait for "Select" button click
  }

  isInstructorIdPresentInSelectedInstructors(id: number): boolean {
    return this.selectedInstructorsId.includes(id);
  }


}
