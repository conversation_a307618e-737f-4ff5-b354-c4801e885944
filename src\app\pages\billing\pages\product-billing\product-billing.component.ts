import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { PurchasedItems, TransactionTypes } from 'src/app/pages/shop/models';
import { StoreProductService } from 'src/app/pages/shop/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBResponse } from 'src/app/shared/models';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { takeUntil } from 'rxjs';
import { AuthService } from 'src/app/auth/services';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { Debounce } from 'src/app/shared/decorators';
import { MatIconModule } from '@angular/material/icon';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { ActivatedRoute } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { MatSidenavModule } from '@angular/material/sidenav';
import { CartDetailsComponent } from 'src/app/pages/billing/pages/product-billing/pages/cart-details/cart-details.component';
import { CommonUtils } from 'src/app/shared/utils';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    SharedModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatIconModule,
    NgxPaginationModule,
    MatSidenavModule
  ],
  COMPONENTS: [CartDetailsComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-product-billing',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './product-billing.component.html',
  styleUrl: './product-billing.component.scss'
})
export class ProductBillingComponent extends BaseComponent implements OnInit {
  studentProducts: PurchasedItems[] = [];
  totalCount!: number;
  currentPage = this.paginationConfig.pageNumber;
  pageSize = this.paginationConfig.itemsPerPage;
  searchTerm!: string;
  selectedDependentId!: number;
  isItemDetailSideNavOpen = false;
  transactionTypes = TransactionTypes;
  selectedCartItems!: PurchasedItems | null;

  constructor(
    private readonly storeProductService: StoreProductService,
    private readonly authService: AuthService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getCustomerOrders(this.currentPage, this.pageSize);
  }

  getCurrentId(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.dependentId) {
        this.selectedDependentId = +params.dependentId;
      } else {
        this.selectedDependentId = 0;
      }
      this.getCustomerOrders((this.currentPage = 1), this.pageSize);
    });
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getCurrentId();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParams(currentPage: number, pageSize: number) {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      Page: currentPage,
      PageSize: pageSize,
      GetPastOrder: true,
      UserId: this.currentUser?.userRoleId === this.constants.roleIds.CLIENT ? this.currentUser?.userId : 0,
      StudentId: this.selectedDependentId
    });
  }

  getCustomerOrders(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.storeProductService
      .getListWithFilters<CBResponse<PurchasedItems>>(this.getFilterParams(currentPage, pageSize), API_URL.storeProduct.getCustomerOrders)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<PurchasedItems>) => {
          this.studentProducts = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  toggleCartItem(isOpen: boolean, cart: PurchasedItems | null): void {
    this.isItemDetailSideNavOpen = isOpen;
    this.selectedCartItems = cart;
  }

  @Debounce(100)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getCustomerOrders(this.currentPage, this.pageSize);
  }
}
