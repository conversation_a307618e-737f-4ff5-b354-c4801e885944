@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.header-tab-only-btn {
  min-height: 50px;
}

::ng-deep {
  .select-box-options-wrapper {
    .mat-mdc-checkbox-checked {
      .mdc-label {
        color: $primary-color !important;
      }
    }
    .mdc-label {
      color: $black-shade-text !important;
    }
  }
}


.auth-page-with-header {
  ::ng-deep {
    .mdc-text-field {
      background-color: $white-color !important;
    }
    .mat-mdc-form-field {
      width: 100% !important;
    }
    .mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper {
      width: 190px;
    }
    .mat-mdc-select-value-text {
      font-size: 14px;
    }
    .select-box-wrapper {
      background: $white-color;
      border: none !important;
    }
  }

  .o-card {
    min-height: calc(100vh - 252px);

    .o-row {
      font-size: 16px;

      .placeholder-name {
        width: 40px;
        height: 40px;
      }

      .student-name-wrapper {
        .student-manager {
          color: $gray-text;
          font-size: 14px;
        }
      }

      .instrument-wrapper {
        @include flex-content-center;

        .instrument-item {
          margin-right: 5px;
        }

        .remaining-instrument-available-count {
          cursor: pointer;
          color: $black-color;
          font-weight: 800;
          font-size: 14px;
        }
      }

      .instructor-name-photo-wrapper {
        @include flex-content-align-center;
        cursor: pointer;
      }

      .text-truncate {
        width: 150px;
      }

      .first-cell {
        text-align: left;
      }

      img {
        height: 18px;
        width: 18px;
      }
    }
  }
}

@media (max-width: 767px) {
  .header-tab-with-btn .action-btn {
    padding: 30px 7px;
  }

  .search-and-count-wrapper-auth {
    display: block !important;
    margin-bottom: 10px;
    .search-and-count-wrapper {
      flex-wrap: wrap;
      margin-bottom: 10px;
    }
    .total-users {
      margin-bottom: 10px;
      span {
        border: none !important;
      }
    }
  }

  .search-bar {
    .search-bar-wrapper {
      margin-left: 0px !important;
      width: 70vw;
    }
  }

  .o-card {
    min-height: calc(100vh - 364px) !important;
    .o-card-body {
      overflow: auto;

      .o-table {
        min-width: fit-content;

        .o-header {
          min-width: 150vw;
        }
        .content {
          height: 355px;
        }
      }
    }
  }
}

::ng-deep {
  .mat-drawer-content {
    overflow: hidden !important;
  }

  .sidenav-content-without-footer {
    overflow: hidden !important;
  }
}
