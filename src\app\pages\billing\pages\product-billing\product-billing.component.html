<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isItemDetailSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-750"
    [disableClose]="true">
    @if (isItemDetailSideNavOpen) {
      <app-cart-details [selectedCartItems]="selectedCartItems" (closeSideNav)="toggleCartItem(false, null)"></app-cart-details>
    }
  </mat-sidenav>

  <mat-sidenav-content>
      <div class="auth-page-wrapper">
          <div class="search-and-count-wrapper-auth">
              <div class="search-and-count-wrapper">
                  <div class="total-users">
                      Total: <span>{{ totalCount }}</span>
                  </div>
                  <div class="search-bar">
                      <mat-form-field class="search-bar-wrapper ms-3">
                          <input matInput placeholder="Search.." [(ngModel)]="searchTerm"
                              (ngModelChange)="onSearchTermChanged()" />
                          <mat-icon matTextPrefix>search</mat-icon>
                      </mat-form-field>
                  </div>
              </div>
          </div>
          <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : productsTemplate"></ng-container>
        </div>
        @if (totalCount > 10) {
          <pagination-controls
              id="product"
              [previousLabel]="''"
              [nextLabel]="''"
              (pageChange)="onPageChange($event)"
              [responsive]="true"
              class="pagination-controls"></pagination-controls>
          }
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #productsTemplate>
    <ng-container [ngTemplateOutlet]="studentProducts.length ? productContent : noDataFound"></ng-container>
</ng-template>

<ng-template #productContent>
    <div class="user-product-container">
        <div class="user-product-wrapper">
            @for (cart of studentProducts | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "product" }; track $index) {
            <div class="accordion-item pointer" (click)="toggleCartItem(true, cart)">
                <div class="cart-header">
                    <div>
                        <div class="plan-name">Order #{{ cart.cartId }}</div>
                        <div class="plan-content">
                            <div class="text-gray">Purchased on
                                <span class="primary-color">{{ cart.purchaseDate | localDate | date }}</span>
                            </div>
                            <div class="dot"></div>
                            <div class="text-gray">For
                                <span class="primary-color">{{ cart.studentName }}</span>
                            </div>
                            <div class="dot"></div>
                            <div class="text-gray">{{ transactionTypes[cart.transactionType] | titlecase }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="plan-pricing">
                    <div class="fw-bold">${{ cart.paidAmount | number:'1.2-2' }}</div>
                    <div class="text-gray">{{ cart.cartItems.length }} products</div>
                </div>
            </div>
            }
        </div>
    </div>
</ng-template>

<ng-template #noDataFound>
    <div class="no-data-found-card">
        <h3>No products purchased yet!</h3>
    </div>
</ng-template>

<ng-template #showLoader>
    <div class="page-loader-wrapper">
        <app-content-loader></app-content-loader>
    </div>
</ng-template>