@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.o-sidebar-wrapper .o-sidebar-body {
  overflow: auto;
  height: calc(100vh - 68px);
}

::ng-deep {
  .search-bar-wrapper-instructor {
    .mdc-text-field {
      background-color: $white-color !important;
      border: 1px solid $btn-options-border-color;
    }
  }

  .search-bar-wrapper .mat-mdc-form-field-flex,
  .search-bar-wrapper .mat-mdc-form-field-focus-overlay,
  .search-bar-wrapper .mat-mdc-text-field-wrapper {
    font-size: 14px;
  }
}

.field-wrapper {
  @include flex-content-align-center;
  margin-bottom: 20px;

  label {
    color: $black-shade-text !important;
    font-size: 16px !important;
    font-weight: 600;
    min-width: 200px;
    margin-bottom: 20px;
  }

  .mat-error-position {
    position: relative;
  }
}

.field-content {
  @include flex-content-align-center;
  width: 100%;

  .dash {
    margin: 0px 3px;
  }
}

.field-with-mat-inputs {
  margin-bottom: 6px;
}

.btn-typed-option-wrap {
  margin-bottom: 8px;
}

label {
  margin-bottom: 16px;
}

::ng-deep {
  .mat-drawer-inner-container {
    overflow: hidden !important;
  }

  .mat-mdc-option .mdc-list-item__primary-text {
    width: 100%;
  }

  .mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text {
    opacity: 1 !important;

    .instructor-name {
      opacity: 0.38 !important;
    }
  }

  .mat-select-custom {
    .mat-mdc-text-field-wrapper {
      font-size: 14px;
      height: 35px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .mdc-text-field__input {
      font-size: 14px !important;
    }

    &.time {
      .mat-mdc-form-field-subscript-wrapper {
        display: none;
      }
    }

    .select-box-options-wrapper {
      width: 480px !important;
      .mat-mdc-checkbox-checked {
      .mdc-label {
        color: $primary-color !important;
      }
    }

      .mdc-label {
        color: $black-shade-text !important;
      }
    }
  }

  .multi-select-chips-wrapper {
    height: 35px;

    .select-placeholder {
      color: $gray-text;
      font-size: 14px;
    }
  }

  .mat-start-date {
    .mat-mdc-text-field-wrapper,
    .mat-mdc-icon-button .mat-mdc-button-touch-target,
    .mat-mdc-button-ripple {
      height: 35px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      height: 35px;
      width: 35px;
    }

    .mat-mdc-icon-button .mat-mdc-button-ripple {
      height: 40px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button svg {
      height: 16px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      padding: 5px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      top: -9px;
    }

    .mat-datepicker-input {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple {
      border-radius: 9%;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before {
      background: transparent !important;
    }
  }
}

@media (max-width: 767px) {
  .field-wrapper,
  .field-content {
    flex-wrap: wrap;

    &.ms-4 {
      margin-left: 0px !important;
    }

    label {
      margin-bottom: 3px !important;
    }

    .dash {
      display: none;
    }
  }
}
